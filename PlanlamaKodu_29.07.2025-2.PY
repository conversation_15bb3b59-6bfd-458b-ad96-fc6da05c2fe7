from itertools import combinations
import math
import pandas as pd
import time
import multiprocessing
from functools import partial
import os

excel_path = r"C:\\Users\\<USER>\\Desktop\\Vestel PLAN_2.xlsx"             # s2,s4,s6
# excel_path = r"C:\\Users\\<USER>\\Documents\\html denemeleri\\python\\game\\Vestel PLAN_2.xlsx"

mantıksal_işlemci = 20

KapasiteTır = 21000
KapasiteKamyon = 11000

UgramaTır = 980
UgramaKamyon = 980

'''
s1 -> parsiyel (anadolu için tldesi dikkate alınır. araç birleştirmesi olmaz)
s2 -> parsiyel (anadolu için araç fiyatı dikkate alınır. araç birleştirmesi yapılır)
s3 -> depolararası diamond (diamond için tldesi dikkate alınır. araç birleştirmesi olmaz)
s4 -> depolararası diamond (diamond için araç fiyatı dikkate alınır. araç birleştirmesi yapılır)
s5 -> depolararası varış aktarma (aktarma için araç fiyatı dikkate alınır. araç birleştirmesi yapılır)
s6 -> alıcı (her alıcı bir noktadır. kendi içinde toplanamaz)(alıcı için araç fiyatı dikkate alınır. araç birleştirmesi yapılır)
'''

# Yardımcı fonksiyonlar
def valid_ldm(coalition, customers, KapasiteTır):
    return sum(customers[i]["ldm"] for i in coalition) <= KapasiteTır

def get_rut_cost(plaka_set, allowed_rules, K_type):
    """Plaka setine uygun rut maliyetini bulur"""
    for rule in allowed_rules:
        if isinstance(rule, tuple) and len(rule) == 3:
            rule_plakalar, kamyon_maliyet, tir_maliyet = rule
            if plaka_set == rule_plakalar:
                return tir_maliyet if K_type == "tır" else kamyon_maliyet
    return None

def valid_plaka_set(plaka_set, allowed_rules):
    if len(plaka_set) > 50: # plaka sayısı 50'den fazla ise
        return False
    if not plaka_set:
        return True
    for rule in allowed_rules:
        # Rut maliyeti varsa tuple formatında, yoksa set formatında
        if isinstance(rule, tuple):
            rule_plakalar = rule[0]  # İlk eleman plaka seti
            if plaka_set.issubset(rule_plakalar):
                return True
        else:
            # Eski format (set)
            if plaka_set.issubset(rule):
                return True
    return False

def coalition_digit_count(assignment, coalition, secim, customers):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_count = sum(1 for i in coalition if assignment[i] == 's6')
    return len(s_set) + s6_count

def valid_coalition_digits(assignment, coalition, secim, customers, allowed_rules):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] == 's6'}
    total_count = coalition_digit_count(assignment, coalition, secim, customers)
    if total_count > 50:    # plaka sayısı 50'den fazla ise
        return False
    union_set = s_set.union(s6_set)
    for rule in allowed_rules:
        # Rut maliyeti varsa tuple formatında, yoksa set formatında
        if isinstance(rule, tuple):
            rule_plakalar = rule[0]  # İlk eleman plaka seti
            if union_set.issubset(rule_plakalar):
                return True
        else:
            # Eski format (set)
            if union_set.issubset(rule):
                return True
    return False

def coalition_size(assignment, coalition, secim, customers):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_count = sum(1 for i in coalition if assignment[i] == 's6')
    return len(s_set) + s6_count

def compute_coalition_min_cost(coalition, customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon, rut_maliyeti_var):
    n = len(coalition)
    best_cost = math.inf
    best_assignment = None
    chosen_K = None
    coalition_list = list(coalition)
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    K_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    sabit_maliyet = UgramaKamyon if K_type == "kamyon" else UgramaTır
    
    def backtrack(idx, current_assignment, current_max_K, current_hacim_loss, current_plaka_set):
        nonlocal best_cost, best_assignment, chosen_K

        current_size = coalition_size(current_assignment, coalition_list[:idx], secim, customers)
        current_cost = current_max_K + current_hacim_loss + ((current_size - 1) * sabit_maliyet)
        if current_cost >= best_cost:
            return

        if idx == n:
            if valid_coalition_digits(current_assignment, coalition_list, secim, customers, allowed_rules):
                c_size = coalition_size(current_assignment, coalition_list, secim, customers)

                # Rut maliyeti hesaplama
                if rut_maliyeti_var:
                    # allowed_rules'dan bu plaka setine uygun rut maliyetini bul
                    rut_max_K = get_rut_cost(current_plaka_set, allowed_rules, K_type)
                    if rut_max_K is not None:
                        final_max_K = rut_max_K
                    else:
                        final_max_K = current_max_K
                else:
                    final_max_K = current_max_K

                cost = final_max_K + current_hacim_loss + ((c_size - 1) * sabit_maliyet)
                if cost < best_cost:
                    best_cost = cost
                    best_assignment = current_assignment.copy()
                    chosen_K = K_type
            return
        
        if not valid_plaka_set(current_plaka_set, allowed_rules):
            return
        
        customer_idx = coalition_list[idx]
        p = customers[customer_idx]
        
        # Sadece seçilen seçenekleri kullan
        options = ['s6'] if p.get("is_full_truck", False) else secim
        
        for option in options:
            new_plaka = p[option]["plaka"]
            new_plaka_set = current_plaka_set | {new_plaka}
            if not valid_plaka_set(new_plaka_set, allowed_rules):
                continue

            # Rut maliyeti varsa, maksimum araç fiyatı hesaplamasını değiştir
            if rut_maliyeti_var:
                # Rut maliyeti varsa, current_max_K'yı güncellemiyoruz
                # Çünkü final hesaplamada rut maliyeti kullanılacak
                new_max_K = current_max_K
            else:
                # Normal hesaplama
                plaka_fiyat = p[option][K_type]
                new_max_K = max(current_max_K, plaka_fiyat)

            new_hacim_loss = current_hacim_loss + (p["hacim"] * p[option]["tldesi"])
            current_assignment[customer_idx] = option
            backtrack(idx + 1, current_assignment, new_max_K, new_hacim_loss, new_plaka_set)
            del current_assignment[customer_idx]
    
    backtrack(0, {}, 0, 0, set())
    if best_cost == math.inf:
        return None, None, None
    return best_cost, best_assignment, chosen_K

def get_valid_masks(N, customers, KapasiteTır):
    return [mask for mask in range(1, 1 << N) if valid_ldm([i for i in range(N) if mask & (1 << i)], customers, KapasiteTır)]

def reconstruct(mask, partition_arr):
    if mask == 0:
        return []
    prev_mask, sub = partition_arr[mask]
    return reconstruct(prev_mask, partition_arr) + [sub]

def clear_output_sheet(excel_path):
    try:
        with pd.ExcelFile(excel_path) as xls:
            if 'output' in xls.sheet_names:
                user_input = input("Mevcut 'output' sekmesindeki verileri temizlemek ister misiniz? (E/H): ").strip().upper()
                if user_input == 'E':
                    empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
                    with pd.ExcelWriter(excel_path, mode='a', if_sheet_exists='replace') as writer:
                        empty_df.to_excel(writer, sheet_name='output', index=False)
                    print("'output' sekmesi temizlendi.")
                    return empty_df
                else:
                    print("Mevcut veriler korunacak.")
                    return pd.read_excel(excel_path, sheet_name="output")
    except Exception as e:
        print(f"Output sekmesi oluşturuluyor: {e}")
        empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
        return empty_df

def preprocess_large_customers(group_df, tarih, secim, truck_prices, KapasiteTır, print_to_terminal):
    new_output_records = []
    updated_rows = []
    
    for _, row in group_df.iterrows():
        if row['hacim'] > KapasiteTır and 's6' in secim:
            full_truck_count = int(row['hacim'] // KapasiteTır)
            remaining_hacim = row['hacim'] % KapasiteTır
            
            # LDM'yi orantılı olarak güncelle
            if row['hacim'] > 0:
                remaining_ldm = row['ldm'] * (remaining_hacim / row['hacim'])
            else:
                remaining_ldm = 0
            
            # Tam tırlar için çıktı oluştur
            plaka_s6 = str(int(row["plaka s6"])).zfill(3)
            cost_per_truck = truck_prices.get(plaka_s6, {}).get('tır', 0)
            
            for _ in range(full_truck_count):
                record = {
                    'tarih': tarih,
                    'grup': str([row['alıcı']]),
                    'secim': str(['s6']),
                    'il': str([row['data_il']]),
                    'hacim': str([KapasiteTır]),
                    'atamalar': str({row['alıcı']: {'seçim': 's6', 'hacim': KapasiteTır}}),
                    'arac': 'tır',
                    'toplam_hacim': KapasiteTır,
                    'maliyet': cost_per_truck
                }
                new_output_records.append(record)
                if print_to_terminal:
                    print(f"Tam tır eklendi: {row['alıcı']}, Hacim: {KapasiteTır}, Maliyet: {cost_per_truck}")
            
            # Kalan hacim için müşteriyi güncelle
            if remaining_hacim > 0:
                new_row = row.copy()
                new_row['hacim'] = remaining_hacim
                new_row['ldm'] = remaining_ldm
                updated_rows.append(new_row)
            else:
                if print_to_terminal:
                    print(f"{row['alıcı']} için tam tırlar eklendi, kalan hacim yok")
        else:
            updated_rows.append(row)
    
    return new_output_records, pd.DataFrame(updated_rows) if updated_rows else pd.DataFrame(columns=group_df.columns)

def process_group(tarih, group_df, secim, print_to_terminal, truck_prices, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon, rut_maliyeti_var):
    sonuclar_grup = []
    if print_to_terminal:
        print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor... (Process ID: {os.getpid()})")
    group_start_time = time.time()
    
    # Büyük hacimli müşterileri ön işleme
    full_truck_records, updated_group_df = preprocess_large_customers(
        group_df, tarih, secim, truck_prices, KapasiteTır, print_to_terminal
    )
    sonuclar_grup.extend(full_truck_records)
    
    # Müşteri listesini oluştur (güncellenmiş verilerle)
    customers = []
    for _, row in updated_group_df.iterrows():
        ldm_value = row["ldm"]
        customer = {
            "alıcı": row["alıcı"],
            "ldm": ldm_value,
            "hacim": row["hacim"],
            "il": row["data_il"],
            "is_full_truck": False
        }
        
        # 's1' ve 's2' seçenekleri
        if 's1' in secim or 's2' in secim:
            if 's1' in secim:
                plaka_s1 = str(int(row["plaka s1"])).zfill(3)
                customer["s1"] = {
                    "plaka": plaka_s1,
                    "kamyon": truck_prices[plaka_s1]['kamyon'],
                    "tır": truck_prices[plaka_s1]['tır'],
                    "tldesi": row["tldesi s1"]
                }
            if 's2' in secim:
                plaka_s2 = str(int(row["plaka s2"])).zfill(3)
                customer["s2"] = {
                    "plaka": plaka_s2,
                    "kamyon": truck_prices[plaka_s2]['kamyon'],
                    "tır": truck_prices[plaka_s2]['tır'],
                    "tldesi": row["tldesi s2"]
                }
        
        # 's3', 's4' ve 's5' seçenekleri
        if 's3' in secim or 's4' in secim or 's5' in secim:
            if 's3' in secim:
                plaka_s3 = str(int(row["plaka s3"])).zfill(3)
                customer["s3"] = {
                    "plaka": plaka_s3,
                    "kamyon": truck_prices[plaka_s3]['kamyon'],
                    "tır": truck_prices[plaka_s3]['tır'],
                    "tldesi": row["tldesi s3"]
                }
            if 's4' in secim:
                plaka_s4 = str(int(row["plaka s4"])).zfill(3)
                customer["s4"] = {
                    "plaka": plaka_s4,
                    "kamyon": truck_prices[plaka_s4]['kamyon'],
                    "tır": truck_prices[plaka_s4]['tır'],
                    "tldesi": row["tldesi s4"]
                }
            if 's5' in secim:
                plaka_s5 = str(int(row["plaka s5"])).zfill(3)
                customer["s5"] = {
                    "plaka": plaka_s5,
                    "kamyon": truck_prices[plaka_s5]['kamyon'],
                    "tır": truck_prices[plaka_s5]['tır'],
                    "tldesi": row["tldesi s5"]
                }
        
        # 's6' seçeneği
        if 's6' in secim:
            plaka_s6 = str(int(row["plaka s6"])).zfill(3)
            customer["s6"] = {
                "plaka": plaka_s6,
                "kamyon": truck_prices[plaka_s6]['kamyon'],
                "tır": truck_prices[plaka_s6]['tır'],
                "tldesi": row["tldesi s6"]
            }
            
        customers.append(customer)

    N = len(customers)
    if N > 0:
        valid_masks = get_valid_masks(N, customers, KapasiteTır)
        valid_coalitions = {}

        for mask in valid_masks:
            coalition = [i for i in range(N) if mask & (1 << i)]
            cost, assignment, K_used = compute_coalition_min_cost(coalition, customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon, rut_maliyeti_var)
            if cost is not None:
                valid_coalitions[mask] = (cost, assignment, K_used)

        dp = [math.inf] * (1 << N)
        partition_arr = [None] * (1 << N)
        dp[0] = 0

        for mask in range(1 << N):
            if dp[mask] == math.inf:
                continue
            remaining = ((1 << N) - 1) ^ mask
            sub = remaining
            while sub:
                if sub in valid_coalitions:
                    new_mask = mask | sub
                    new_cost = dp[mask] + valid_coalitions[sub][0]
                    if new_cost < dp[new_mask]:
                        dp[new_mask] = new_cost
                        partition_arr[new_mask] = (mask, sub)
                sub = (sub - 1) & remaining

        final_partition = reconstruct((1 << N) - 1, partition_arr) if dp[(1 << N) - 1] != math.inf else []
        
        for sub in final_partition:
            cost, assignment, K_used = valid_coalitions[sub]
            coalition_names = [customers[i]["alıcı"] for i in range(N) if sub & (1 << i)]
            coalition_country = [customers[i]["il"] for i in range(N) if sub & (1 << i)]
            coalition_ldm = [int(customers[i]["ldm"]) for i in range(N) if sub & (1 << i)]
            coalition_assignment = [assignment[i] for i in range(N) if sub & (1 << i)]
            
            toplam_hacim = int(sum(customers[i]["hacim"] for i in range(N) if sub & (1 << i)))
            
            if print_to_terminal:
                print(f"Grup: {coalition_names}, Kullanılan Araç: {K_used}, Toplam Hacim: {toplam_hacim}, Grup Maliyeti: {cost}")
            
            sonuclar_grup.append({
                'tarih': tarih,
                'grup': str(coalition_names),
                'secim': str(coalition_assignment),
                'il': str(coalition_country),
                'hacim': str(coalition_ldm),
                'arac': K_used,
                'toplam_hacim': toplam_hacim,
                'maliyet': cost
            })
    else:
        if print_to_terminal:
            print(f"Kalan müşteri yok, sadece tam tırlar eklendi.")
    
    group_duration = time.time() - group_start_time
    if print_to_terminal:
        print(f"Bu grup işlemi {group_duration:.2f} saniye sürdü (Process ID: {os.getpid()})")
    
    return sonuclar_grup

if __name__ == '__main__':
    # Kullanıcı girişlerini sadece ana işlemde al
    print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == 'E'

    # Rut fiyatı sorusu
    rut_maliyeti_var = input("Rut fiyatı var mı? (E/H): ").strip().upper() == 'E'

    # Uğrama fiyatlarını rut durumuna göre ayarla
    if rut_maliyeti_var:
        UgramaTır = 0
        UgramaKamyon = 0
    else:
        UgramaTır = 980
        UgramaKamyon = 980

    secim_input = input("Kullanılacak seçenekleri virgülle girin (s1,s2,s3,s4,s5,s6): ").strip().lower()
    secim = [s.strip() for s in secim_input.split(',')] if secim_input else ['s1','s2','s3','s4','s5','s6']
    print(f"Seçilen seçenekler: {secim}")
    print(f"Rut maliyeti kullanılacak: {'Evet' if rut_maliyeti_var else 'Hayır'}")

    # Output sekmesini temizle (sadece ana işlemde)
    output_df = clear_output_sheet(excel_path)

    # Tüm işlemlerin toplam süresi için
    total_start_time = time.time()
    
    # Tüm veriyi oku
    customers_df = pd.read_excel(excel_path, sheet_name="customers")
    customers_df['tarih'] = pd.to_datetime(customers_df['tarih'])
    grouped_customers = [(tarih, group) for tarih, group in customers_df.groupby('tarih')]

    # Truckprices ve allowed_rules önceden oku
    truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
    truck_prices = {}
    for _, row in truck_prices_df.iterrows():
        plaka = str(int(row['plaka'])).zfill(3)
        truck_prices[plaka] = {'kamyon': row['kamyon'], 'tır': row['tır']}

    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = []

    if rut_maliyeti_var:
        # Üç sütunlu format: A: plaka seti, B: kamyon maliyeti, C: tır maliyeti
        for _, row in rules_df.iterrows():
            plakalar = {s.strip().zfill(3) for s in str(row[0]).split(",")}
            kamyon_maliyet = float(row[1])
            tir_maliyet = float(row[2])
            allowed_rules.append((plakalar, kamyon_maliyet, tir_maliyet))
    else:
        # Tek sütunlu format (eski)
        allowed_rules = rules_df[0].apply(lambda x: {s.strip() for s in str(x).split(",")}).tolist()

    # Paralel işlem için fonksiyonu hazırla
    process_group_partial = partial(
        process_group,
        secim=secim,
        print_to_terminal=print_to_terminal,
        truck_prices=truck_prices,
        allowed_rules=allowed_rules,
        KapasiteTır=KapasiteTır,
        KapasiteKamyon=KapasiteKamyon,
        UgramaTır=UgramaTır,
        UgramaKamyon=UgramaKamyon,
        rut_maliyeti_var=rut_maliyeti_var
    )

    # Paralel işlem
    with multiprocessing.Pool(processes=mantıksal_işlemci) as pool:
        results = pool.starmap(process_group_partial, grouped_customers)

    # Sonuçları birleştir
    sonuclar = []
    for res in results:
        sonuclar.extend(res)

    # Toplam süre hesaplama
    total_duration = time.time() - total_start_time
    if print_to_terminal:
        print(f"\nTOPLAM İŞLEM SÜRESİ: {total_duration:.2f} saniye")
        
    # Excel'e yaz
    yeni_sonuclar_df = pd.DataFrame(sonuclar)
    if not output_df.empty:
        output_df = pd.concat([output_df, yeni_sonuclar_df], ignore_index=True)
    else:
        output_df = yeni_sonuclar_df

    with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        output_df.to_excel(writer, sheet_name='output', index=False)

    print("\nSonuçlar başarıyla Excel'e kaydedildi.")
