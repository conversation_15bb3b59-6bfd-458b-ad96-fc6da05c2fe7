from itertools import combinations
import math
import pandas as pd
import time
import multiprocessing
from functools import partial
import os
from openpyxl import load_workbook, Workbook

excel_path = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\Rota_oluşturma\\Vestel PLAN_2.xlsx"
mantıksal_çekirdek_sayısı = 10  # Mantıksal çekirdek sayısını burada ayarlayın

KapasiteTır = 21000
KapasiteKamyon = 11000

UgramaTır = 980
UgramaKamyon = 980

def valid_ldm(coalition, customers, KapasiteTır):
    return sum(customers[i]["ldm"] for i in coalition) <= KapasiteTır

def valid_plaka_set(plaka_set, allowed_rules):
    if len(plaka_set) > 50:
        return False
    if not plaka_set:
        return True
    for rule in allowed_rules:
        if plaka_set.issubset(rule[0]):
            return True
    return False

def coalition_digit_count(assignment, coalition, secim, customers):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_count = sum(1 for i in coalition if assignment[i] == 's6')
    return len(s_set) + s6_count

def valid_coalition_digits(assignment, coalition, secim, customers, allowed_rules):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] == 's6'}
    total_count = coalition_digit_count(assignment, coalition, secim, customers)
    if total_count > 50:
        return False
    union_set = s_set.union(s6_set)
    for rule in allowed_rules:
        if union_set.issubset(rule[0]):
            return True
    return False

def coalition_size(assignment, coalition, secim, customers):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_count = sum(1 for i in coalition if assignment[i] == 's6')
    return len(s_set) + s6_count

def compute_coalition_min_cost(coalition, customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon, rut_maliyeti_var):
    n = len(coalition)
    best_cost = math.inf
    best_assignment = None
    chosen_K = None
    coalition_list = list(coalition)
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    K_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    sabit_maliyet = UgramaKamyon if K_type == "kamyon" else UgramaTır
    
    if rut_maliyeti_var:
        def backtrack_rut(idx, current_assignment, current_plaka_set, current_hacim_loss=0):
            nonlocal best_cost, best_assignment, chosen_K
            
            if len(current_plaka_set) > 50:
                return
                
            if idx == n:
                min_rule_cost = math.inf
                for rule in allowed_rules:
                    rule_set, kamyon_cost, tir_cost = rule
                    if current_plaka_set.issubset(rule_set):
                        rule_cost = kamyon_cost if K_type == "kamyon" else tir_cost
                        if rule_cost < min_rule_cost:
                            min_rule_cost = rule_cost
                
                # HACİM KAYBI EKLENDİ
                total_cost = min_rule_cost + current_hacim_loss
                if total_cost < best_cost:
                    best_cost = total_cost
                    best_assignment = current_assignment.copy()
                    chosen_K = K_type
                return
            
            customer_idx = coalition_list[idx]
            p = customers[customer_idx]
            options = ['s6'] if p.get("is_full_truck", False) else secim
            
            for option in options:
                new_plaka = p[option]["plaka"]
                new_plaka_set = current_plaka_set | {new_plaka}
                
                # HACİM KAYBI HESAPLAMA EKLENDİ
                hacim_kaybi = p["hacim"] * p[option]["tldesi"]
                new_hacim_loss = current_hacim_loss + hacim_kaybi
                
                current_assignment[customer_idx] = option
                backtrack_rut(idx + 1, current_assignment, new_plaka_set, new_hacim_loss)
                del current_assignment[customer_idx]
        
        backtrack_rut(0, {}, set(), 0)
    
    else:
        def backtrack_eski(idx, current_assignment, current_max_K, current_hacim_loss, current_plaka_set):
            nonlocal best_cost, best_assignment, chosen_K
            
            current_size = coalition_size(current_assignment, coalition_list[:idx], secim, customers)
            current_cost = current_max_K + current_hacim_loss + ((current_size - 1) * sabit_maliyet)
            if current_cost >= best_cost:
                return
            
            if idx == n:
                if valid_coalition_digits(current_assignment, coalition_list, secim, customers, allowed_rules):
                    c_size = coalition_size(current_assignment, coalition_list, secim, customers)
                    cost = current_max_K + current_hacim_loss + ((c_size - 1) * sabit_maliyet)
                    if cost < best_cost:
                        best_cost = cost
                        best_assignment = current_assignment.copy()
                        chosen_K = K_type
                return
            
            if not valid_plaka_set(current_plaka_set, allowed_rules):
                return
            
            customer_idx = coalition_list[idx]
            p = customers[customer_idx]
            options = ['s6'] if p.get("is_full_truck", False) else secim
            
            for option in options:
                new_plaka = p[option]["plaka"]
                new_plaka_set = current_plaka_set | {new_plaka}
                if not valid_plaka_set(new_plaka_set, allowed_rules):
                    continue
                
                plaka_fiyat = p[option][K_type]
                new_max_K = max(current_max_K, plaka_fiyat)
                new_hacim_loss = current_hacim_loss + (p["hacim"] * p[option]["tldesi"])
                current_assignment[customer_idx] = option
                backtrack_eski(idx + 1, current_assignment, new_max_K, new_hacim_loss, new_plaka_set)
                del current_assignment[customer_idx]
        
        backtrack_eski(0, {}, 0, 0, set())
    
    if best_cost == math.inf:
        return None, None, None
    return best_cost, best_assignment, chosen_K

def get_valid_masks(N, customers, KapasiteTır):
    return [mask for mask in range(1, 1 << N) if valid_ldm([i for i in range(N) if mask & (1 << i)], customers, KapasiteTır)]

def reconstruct(mask, partition_arr):
    if mask == 0:
        return []
    prev_mask, sub = partition_arr[mask]
    return reconstruct(prev_mask, partition_arr) + [sub]

def clear_output_sheet(excel_path):
    """Excel dosyasındaki output sayfasını temizler veya oluşturur"""
    try:
        wb = load_workbook(excel_path)
        if 'output' in wb.sheetnames:
            user_input = input("Mevcut 'output' sekmesindeki verileri temizlemek ister misiniz? (E/H): ").strip().upper()
            if user_input == 'E':
                del wb['output']
                ws = wb.create_sheet('output')
                headers = ['tarih', 'grup', 'secim', 'il', 'hacim', 'atamalar', 'arac', 'toplam_hacim', 'maliyet']
                ws.append(headers)
                wb.save(excel_path)
                print("'output' sekmesi temizlendi.")
            else:
                print("Mevcut veriler korunacak.")
        else:
            ws = wb.create_sheet('output')
            headers = ['tarih', 'grup', 'secim', 'il', 'hacim', 'atamalar', 'arac', 'toplam_hacim', 'maliyet']
            ws.append(headers)
            wb.save(excel_path)
            print("'output' sekmesi oluşturuldu.")
        wb.close()
    except Exception as e:
        print(f"Hata oluştu: {e}")
        # Dosya yoksa yeni oluştur
        wb = Workbook()
        if 'Sheet' in wb.sheetnames:
            del wb['Sheet']
        ws = wb.create_sheet('output')
        headers = ['tarih', 'grup', 'secim', 'il', 'hacim', 'atamalar', 'arac', 'toplam_hacim', 'maliyet']
        ws.append(headers)
        wb.save(excel_path)
        wb.close()

def write_results_to_excel(results, excel_path):
    """Sonuçları Excel dosyasına yazar"""
    try:
        wb = load_workbook(excel_path)
        if 'output' not in wb.sheetnames:
            ws = wb.create_sheet('output')
            headers = ['tarih', 'grup', 'secim', 'il', 'hacim', 'atamalar', 'arac', 'toplam_hacim', 'maliyet']
            ws.append(headers)
        else:
            ws = wb['output']
        
        for result in results:
            row = [
                result['tarih'],
                result['grup'],
                result['secim'],
                result['il'],
                result['hacim'],
                result['atamalar'],
                result['arac'],
                result['toplam_hacim'],
                result['maliyet']
            ]
            ws.append(row)
        
        wb.save(excel_path)
        wb.close()
        return True
    except Exception as e:
        print(f"Excel'e yazma hatası: {e}")
        return False

def preprocess_large_customers(group_df, tarih, secim, truck_prices, allowed_rules, KapasiteTır, print_to_terminal, rut_maliyeti_var):
    new_output_records = []
    updated_rows = []
    
    for _, row in group_df.iterrows():
        if row['hacim'] > KapasiteTır and 's6' in secim:
            full_truck_count = row['hacim'] // KapasiteTır
            remaining_hacim = row['hacim'] % KapasiteTır
            
            if row['hacim'] > 0:
                remaining_ldm = row['ldm'] * (remaining_hacim / row['hacim'])
            else:
                remaining_ldm = 0
            
            plaka_s6 = str(int(row["plaka s6"])).zfill(3)
            
            # HACİM KAYBI HESAPLAMA EKLENDİ (HER TAM TIR İÇİN)
            hacim_kaybi_per_truck = KapasiteTır * row['tldesi s6']
            
            if rut_maliyeti_var:
                min_cost = math.inf
                for rule in allowed_rules:
                    rule_set, kamyon_cost, tir_cost = rule
                    if plaka_s6 in rule_set and tir_cost < min_cost:
                        min_cost = tir_cost
                # HACİM KAYBI EKLENDİ
                cost_per_truck = min_cost + hacim_kaybi_per_truck
            else:
                # HACİM KAYBI EKLENDİ
                cost_per_truck = truck_prices.get(plaka_s6, {}).get('tır', 0) + hacim_kaybi_per_truck
            
            for _ in range(int(full_truck_count)):
                record = {
                    'tarih': tarih,
                    'grup': str([row['alıcı']]),
                    'secim': str(['s6']),
                    'il': str([row['data_il']]),
                    'hacim': str([KapasiteTır]),
                    'atamalar': str({row['alıcı']: {'seçim': 's6', 'hacim': KapasiteTır}}),
                    'arac': 'tır',
                    'toplam_hacim': KapasiteTır,
                    'maliyet': cost_per_truck
                }
                new_output_records.append(record)
                if print_to_terminal:
                    print(f"Tam tır eklendi: {row['alıcı']}, Hacim: {KapasiteTır}, Maliyet: {cost_per_truck}")
            
            if remaining_hacim > 0:
                new_row = row.copy()
                new_row['hacim'] = remaining_hacim
                new_row['ldm'] = remaining_ldm
                updated_rows.append(new_row)
            else:
                if print_to_terminal:
                    print(f"{row['alıcı']} için tam tırlar eklendi, kalan hacim yok")
        else:
            updated_rows.append(row)
    
    return new_output_records, pd.DataFrame(updated_rows) if updated_rows else pd.DataFrame(columns=group_df.columns)

def process_group(tarih, group_df, secim, print_to_terminal, truck_prices, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon, rut_maliyeti_var):
    sonuclar_grup = []
    if print_to_terminal:
        print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor... (Process ID: {os.getpid()})")
    group_start_time = time.time()
    
    full_truck_records, updated_group_df = preprocess_large_customers(
        group_df, tarih, secim, truck_prices, allowed_rules, KapasiteTır, print_to_terminal, rut_maliyeti_var
    )
    sonuclar_grup.extend(full_truck_records)
    
    customers = []
    for _, row in updated_group_df.iterrows():
        customer = {
            "alıcı": row["alıcı"],
            "ldm": row["ldm"],
            "hacim": row["hacim"],
            "il": row["data_il"],
            "is_full_truck": False
        }
        
        if 's1' in secim or 's2' in secim:
            if 's1' in secim:
                plaka_s1 = str(int(row["plaka s1"])).zfill(3)
                customer["s1"] = {
                    "plaka": plaka_s1,
                    "kamyon": truck_prices[plaka_s1]['kamyon'],
                    "tır": truck_prices[plaka_s1]['tır'],
                    "tldesi": row["tldesi s1"]
                }
            if 's2' in secim:
                plaka_s2 = str(int(row["plaka s2"])).zfill(3)
                customer["s2"] = {
                    "plaka": plaka_s2,
                    "kamyon": truck_prices[plaka_s2]['kamyon'],
                    "tır": truck_prices[plaka_s2]['tır'],
                    "tldesi": row["tldesi s2"]
                }
        
        if 's3' in secim or 's4' in secim or 's5' in secim:
            if 's3' in secim:
                plaka_s3 = str(int(row["plaka s3"])).zfill(3)
                customer["s3"] = {
                    "plaka": plaka_s3,
                    "kamyon": truck_prices[plaka_s3]['kamyon'],
                    "tır": truck_prices[plaka_s3]['tır'],
                    "tldesi": row["tldesi s3"]
                }
            if 's4' in secim:
                plaka_s4 = str(int(row["plaka s4"])).zfill(3)
                customer["s4"] = {
                    "plaka": plaka_s4,
                    "kamyon": truck_prices[plaka_s4]['kamyon'],
                    "tır": truck_prices[plaka_s4]['tır'],
                    "tldesi": row["tldesi s4"]
                }
            if 's5' in secim:
                plaka_s5 = str(int(row["plaka s5"])).zfill(3)
                customer["s5"] = {
                    "plaka": plaka_s5,
                    "kamyon": truck_prices[plaka_s5]['kamyon'],
                    "tır": truck_prices[plaka_s5]['tır'],
                    "tldesi": row["tldesi s5"]
                }
        
        if 's6' in secim:
            plaka_s6 = str(int(row["plaka s6"])).zfill(3)
            customer["s6"] = {
                "plaka": plaka_s6,
                "kamyon": truck_prices[plaka_s6]['kamyon'],
                "tır": truck_prices[plaka_s6]['tır'],
                "tldesi": row["tldesi s6"]
            }
            
        customers.append(customer)

    N = len(customers)
    if N > 0:
        valid_masks = get_valid_masks(N, customers, KapasiteTır)
        valid_coalitions = {}

        for mask in valid_masks:
            coalition = [i for i in range(N) if mask & (1 << i)]
            cost, assignment, K_used = compute_coalition_min_cost(
                coalition, customers, secim, allowed_rules, 
                KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon, rut_maliyeti_var
            )
            if cost is not None:
                valid_coalitions[mask] = (cost, assignment, K_used)

        dp = [math.inf] * (1 << N)
        partition_arr = [None] * (1 << N)
        dp[0] = 0

        for mask in range(1 << N):
            if dp[mask] == math.inf:
                continue
            remaining = ((1 << N) - 1) ^ mask
            sub = remaining
            while sub:
                if sub in valid_coalitions:
                    new_mask = mask | sub
                    new_cost = dp[mask] + valid_coalitions[sub][0]
                    if new_cost < dp[new_mask]:
                        dp[new_mask] = new_cost
                        partition_arr[new_mask] = (mask, sub)
                sub = (sub - 1) & remaining

        final_partition = reconstruct((1 << N) - 1, partition_arr) if dp[(1 << N) - 1] != math.inf else []
        
        for sub in final_partition:
            cost, assignment, K_used = valid_coalitions[sub]
            coalition_names = [customers[i]["alıcı"] for i in range(N) if sub & (1 << i)]
            coalition_country = [customers[i]["il"] for i in range(N) if sub & (1 << i)]
            coalition_ldm = [customers[i]["ldm"] for i in range(N) if sub & (1 << i)]
            coalition_assignment = [assignment[i] for i in range(N) if sub & (1 << i)]

            assignment_details = {
                customers[i]["alıcı"]: {
                    'seçim': assignment[i],
                    'hacim': customers[i]["hacim"]
                } for i in assignment if sub & (1 << i)
            }
            
            toplam_hacim = sum(customers[i]["hacim"] for i in range(N) if sub & (1 << i))
            
            if print_to_terminal:
                print(f"Grup: {coalition_names}, Grup Atamaları: {assignment_details}, Kullanılan Araç: {K_used}, Toplam Hacim: {toplam_hacim}, Grup Maliyeti: {cost}")
            
            sonuclar_grup.append({
                'tarih': tarih,
                'grup': str(coalition_names),
                'secim': str(coalition_assignment),
                'il': str(coalition_country),
                'hacim': str(coalition_ldm),
                'atamalar': str(assignment_details),
                'arac': K_used,
                'toplam_hacim': toplam_hacim,
                'maliyet': cost
            })
    else:
        if print_to_terminal:
            print(f"Kalan müşteri yok, sadece tam tırlar eklendi.")
    
    group_duration = time.time() - group_start_time
    if print_to_terminal:
        print(f"Bu grup işlemi {group_duration:.2f} saniye sürdü (Process ID: {os.getpid()})")
    
    return sonuclar_grup

if __name__ == '__main__':
    print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == 'E'
    rut_maliyeti_var = input("Rut maliyeti var mı? (E/H): ").strip().upper() == 'E'
    
    secim_input = input("Kullanılacak seçenekleri virgülle girin (s1,s2,s3,s4,s5,s6): ").strip().lower()
    secim = [s.strip() for s in secim_input.split(',')] if secim_input else ['s1','s2','s3','s4','s5','s6']
    print(f"Seçilen seçenekler: {secim}")

    # Output sayfasını temizle veya oluştur
    clear_output_sheet(excel_path)

    total_start_time = time.time()
    
    customers_df = pd.read_excel(excel_path, sheet_name="customers")
    customers_df['tarih'] = pd.to_datetime(customers_df['tarih'])
    grouped_customers = [(tarih, group) for tarih, group in customers_df.groupby('tarih')]

    truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
    truck_prices = {}
    for _, row in truck_prices_df.iterrows():
        plaka = str(int(row['plaka'])).zfill(3)
        truck_prices[plaka] = {'kamyon': row['kamyon'], 'tır': row['tır']}

    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = []
    
    if rut_maliyeti_var:
        for _, row in rules_df.iterrows():
            plakalar = {s.strip().zfill(3) for s in str(row[0]).split(",")}
            kamyon_maliyet = float(row[1])
            tir_maliyet = float(row[2])
            allowed_rules.append((plakalar, kamyon_maliyet, tir_maliyet))
    else:
        for _, row in rules_df.iterrows():
            plakalar = {s.strip().zfill(3) for s in str(row[0]).split(",")}
            allowed_rules.append((plakalar, 0, 0))

    process_group_partial = partial(
        process_group,
        secim=secim,
        print_to_terminal=print_to_terminal,
        truck_prices=truck_prices,
        allowed_rules=allowed_rules,
        KapasiteTır=KapasiteTır,
        KapasiteKamyon=KapasiteKamyon,
        UgramaTır=UgramaTır,
        UgramaKamyon=UgramaKamyon,
        rut_maliyeti_var=rut_maliyeti_var
    )

    # 1 çekirdek Excel yazma için ayrılıyor
    worker_count = max(1, mantıksal_çekirdek_sayısı - 1)
    
    with multiprocessing.Pool(processes=worker_count) as pool:
        # Her grup sonucu için işlem
        for i, (tarih, group_df) in enumerate(grouped_customers):
            sonuclar_grup = process_group_partial(tarih, group_df)
            if sonuclar_grup:
                success = write_results_to_excel(sonuclar_grup, excel_path)
                if success and print_to_terminal:
                    print(f"{i+1}/{len(grouped_customers)}. grup sonuçları Excel'e yazıldı.")
            else:
                if print_to_terminal:
                    print(f"{i+1}/{len(grouped_customers)}. grup için sonuç bulunamadı.")

    total_duration = time.time() - total_start_time
    if print_to_terminal:
        print(f"\nTOPLAM İŞLEM SÜRESİ: {total_duration:.2f} saniye")
        
    print("\nTüm sonuçlar başarıyla Excel'e kaydedildi.")